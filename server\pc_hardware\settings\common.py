import os
from datetime import timedelta
from pathlib import Path
import cloudinary
from dotenv import load_dotenv
from os import getenv

load_dotenv()
# import cloudinary.uploader
# import cloudinary.api

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

INTERNAL_IPS = [
    "127.0.0.1",
]

INSTALLED_APPS = [
    'admin_interface',
    'colorfield',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'drf_spectacular',
    'mptt',
    'rest_framework',
    'social_django',
    'django_filters',
    'corsheaders',
    'cloudinary',
    'phonenumber_field',
    'ordered_model',
    'apps.core',
    'apps.staff',
    'apps.products',
    'apps.cart',
    'apps.order',
    'apps.payments',
    'apps.customers',
    'apps.wishlist'
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'apps.staff.middleware.StaffAPILoggingMiddleware',
]

ROOT_URLCONF = 'pc_hardware.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            os.path.join(BASE_DIR, 'pc_hardware', 'templates'),
            os.path.join(BASE_DIR, 'templates'),  # Add our custom templates directory
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'pc_hardware.wsgi.application'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'

LANGUAGES = [
    ('en', 'English'),
    # ('ru', 'Russian'),
]

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'pc_hardware', 'static'),
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# CORS Settings
CORS_ALLOWED_ORIGINS = [
    # "http://localhost:4173",
    "http://localhost:3000",
    "https://picky-store.netlify.app",
    "http://localhost:3500"
]

CORS_ALLOW_CREDENTIALS = True

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

"""
A list that specifies the authentication schemes or backends that Django should use
to authenticate users.
"""
AUTHENTICATION_BACKENDS = [
    'social_core.backends.facebook.FacebookOAuth2',
    'social_core.backends.google.GoogleOAuth2',
    # 'social_core.backends.facebook.FacebookOAuth2',
    'django.contrib.auth.backends.ModelBackend',  # Default backend (Used to login for admin panel, etc.)
]

# Using custom user model
AUTH_USER_MODEL = 'core.User'

# AUTH_COOKIE = 'access'
AUTH_COOKIE = 'access'
AUTH_COOKIE_ACCESS_MAX_AGE = 90 * 24 * 60 * 60  # 90 days in seconds
AUTH_COOKIE_REFRESH_MAX_AGE = 90 * 24 * 60 * 60  # 90 days in seconds

"""
If this is set to True, the cookie will only be sent over HTTPS connections.
When AUTH_COOKIE_SAMESITE = 'None' AND setting AUTH_COOKIE_SECURE (secure=True) 
to False will prevent from attaching cookie to the request even if it is in 
development mode.
"""
AUTH_COOKIE_SECURE = True  # on development
# Prevents JavaScript from accessing the cookie
AUTH_COOKIE_HTTP_ONLY = True

# Defines the path scope of the cookie; the cookie is accessible to all URLs on the site
AUTH_COOKIE_PATH = '/'
# Controls how cookies are sent between different sites
# 'None' allows cookies to be sent in all cross-site contexts
AUTH_COOKIE_SAMESITE = 'None'
# Domain to which the cookie is accessible; restricts cookie access to this domain
AUTH_COOKIE_DOMAIN = 'localhost'

SESSION_COOKIE_SAMESITE = 'Lax'
# SESSION_COOKIE_HTTPONLY = True
# SESSION_COOKIE_SECURE = False  # Set to True if you're using HTTPS

SOCIAL_AUTH_USER_MODEL = 'core.User'
# SOCIAL_AUTH_CREATE_USERS = True

LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = getenv('SOCIAL_AUTH_GOOGLE_OAUTH2_KEY')
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = getenv('GOOGLE_OAUTH2_SECRET')

"""
additional parameters that you want to include in the request to Google
when retrieving user profile information.
"""
SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE = [
    # 'https://www.googleapis.com/auth/userinfo.email',
    # 'https://www.googleapis.com/auth/userinfo.profile',
    # 'openid',  # standardized authentication protocol built on top of OAuth2.0
    'profile',  # provides access to user's profile information
    'email',  # provides access to user's email address
]
SOCIAL_AUTH_REDIRECT_IS_HTTPS = True
# SOCIAL_AUTH_GOOGLE_OAUTH2_EXTRA_DATA = ['first_name', 'last_name']

SOCIAL_AUTH_FACEBOOK_KEY = getenv('SOCIAL_AUTH_FACEBOOK_KEY')
SOCIAL_AUTH_FACEBOOK_SECRET = getenv('SOCIAL_AUTH_FACEBOOK_SECRET')
SOCIAL_AUTH_FACEBOOK_SCOPE = ['email', 'public_profile']
SOCIAL_AUTH_FACEBOOK_PROFILE_EXTRA_PARAMS = {
    'fields': 'id, email, first_name, last_name',
}
SOCIAL_AUTH_FACEBOOK_CREATE_USERS = True

# CSRF and SSL configurations for cookies (Can make this False for development)
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_SECURE = True

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_FROM = '<EMAIL>'
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'lahmwtvdkrxnsnxm'
EMAIL_PORT = 587
EMAIL_USE_TLS = True

DOMAIN = 'localhost:3000'
SITE_NAME = 'Picky-PC Hardware Store'
FRONTEND_URL = 'http://localhost:3000'

REST_FRAMEWORK = {
    # Prevent rendering decimals as string
    'COERCE_DECIMAL_TO_STRING': False,
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'apps.core.authentication.CustomJWTAuthentication',  # JWT Authentication
    ),

    # 'DEFAULT_FILTER_BACKENDS': (
    #     'django_filters.rest_framework.DjangoFilterBackend',
    # ),
    # 'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    # 'DEFAULT_RENDERER_CLASSES': [
    # 'rest_framework.renderers.JSONRenderer',
    # 'rest_framework.renderers.BrowsableAPIRenderer',
    # 'rest_framework_xml.renderers.XMLRenderer',
    # ],
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=90),  # Regular users
    "REFRESH_TOKEN_LIFETIME": timedelta(days=90),
    "AUTH_HEADER_TYPES": ('Bearer',),
    "BLACKLIST_AFTER_ROTATION": True,
    "ROTATE_REFRESH_TOKENS": True,
    "AUTH_TOKEN_CLASSES": ('rest_framework_simplejwt.tokens.AccessToken',),

    # Staff-specific settings (can be overridden in views)
    "STAFF_ACCESS_TOKEN_LIFETIME": timedelta(hours=8),  # 8 hours for staff
    "STAFF_REFRESH_TOKEN_LIFETIME": timedelta(days=7),  # 7 days for staff
}

# DJOSER = {
#     # 'LOGIN_FIELD': 'email',  # or 'username' if you prefer
#     'TOKEN_MODEL': None,
#     'USER_CREATE_PASSWORD_RETYPE': True,
#     # 'USERNAME_CHANGED_EMAIL_CONFIRMATION': True,
#     # 'PASSWORD_CHANGED_EMAIL_CONFIRMATION': True,
#     # 'SEND_CONFIRMATION_EMAIL': True,
#     # 'SET_USERNAME_RETYPE': True,
#     # 'SET_PASSWORD_RETYPE': True,
#     # 'PASSWORD_RESET_CONFIRM_URL': 'password/reset/confirm/{uid}/{token}',
#     # 'USERNAME_RESET_CONFIRM_URL': 'email/reset/confirm/{uid}/{token}',
#     # 'ACTIVATION_URL': 'activate/{uid}/{token}',
#     # 'SEND_ACTIVATION_EMAIL': True,
#     'SOCIAL_AUTH_TOKEN_STRATEGY': 'djoser.social.token.jwt.TokenStrategy',
#
#     'SOCIAL_AUTH_ALLOWED_REDIRECT_URIS': ['http://localhost:3000/auth/google', 'http://localhost:3000/auth/facebook'],
#     'SERIALIZERS': {
#         'user_create': 'apps.core.serializers.ConsolidatedUserSerializer',
#         'user': 'apps.core.serializers.ConsolidatedUserSerializer',
#     },
# }

cloudinary.config(
    cloud_name=getenv('CLOUDINARY_CLOUD_NAME'),
    api_key=getenv('CLOUDINARY_API_KEY'),
    api_secret=getenv('CLOUDINARY_API_SECRET')
)

SPECTACULAR_SETTINGS = {
    'TITLE': 'Picky E-Commerce RESTful APIs',
    # 'DESCRIPTION': 'Your API Description',
    'VERSION': '1.0.0',
    # ... other options
}

# Stripe settings
STRIPE_SECRET_KEY = getenv('STRIPE_SECRET_KEY')
STRIPE_WEBHOOK_SECRET = getenv('STRIPE_WEBHOOK_SECRET')

# PayPal settings
PAYPAL_CLIENT_ID = getenv('PAYPAL_CLIENT_ID')
PAYPAL_CLIENT_SECRET = getenv('PAYPAL_CLIENT_SECRET')
PAYPAL_BASE_URL = getenv('PAYPAL_BASE_URL')
PAYPAL_WEBHOOK_ID = getenv('PAYPAL_WEBHOOK_ID')

# PayPal Braintree settings
# BRAINTREE_MERCHANT_ID = '9bwqdm8dqz9mf842'
# BRAINTREE_PUBLIC_KEY = 'm76nwyy735wzx5xf'
# BRAINTREE_PRIVATE_KEY = '********************************'
# BRAINTREE_ENVIRONMENT = 'sandbox'  # or 'production'

MAX_CART_WEIGHT = 20000
STORE_OWNER_EMAIL = '<EMAIL>'

PHONENUMBER_DEFAULT_REGION = 'US'  # Change this to your default region

# Twilio settings
TWILIO_ACCOUNT_SID = getenv('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = getenv('TWILIO_AUTH_TOKEN')
TWILIO_PHONE_NUMBER = 'your_twilio_phone_number'

# Vonage settings
VONAGE_API_KEY = getenv('VONAGE_API_KEY')
VONAGE_API_SECRET = getenv('VONAGE_API_SECRET')
VONAGE_BRAND_NAME = 'PickyPC'
