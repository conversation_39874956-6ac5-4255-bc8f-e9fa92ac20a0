// Product variants management page
// Comprehensive variant management with SKU, pricing, and inventory

import React, { useState } from 'react'
import { useParams, Link } from '@tanstack/react-router'
import { FiPlus, FiEdit, FiTrash2, FiArrowLeft, FiPackage } from 'react-icons/fi'
import { useProduct, useProductVariants, useCreateVariant, useDeleteVariant } from '../../../hooks/use-products'
import { Card } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { DataTable } from '../../../components/ui/DataTable'
import { Badge, StockStatusBadge } from '../../../components/ui/Badge'
import { Modal } from '../../../components/ui/Modal'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { PermissionGuard } from '../../../components/auth/AuthGuard'
import { ProductVariant } from '../../../types/api-types'
import { formatCurrency } from '../../../utils/format'
import { VariantFormModal } from '../../../components/products/VariantFormModal'
import styles from './ProductVariantsPage.module.scss'

export const ProductVariantsPage: React.FC = () => {
  const params = useParams({ from: '/products/$productId/variants' })
  const productId = parseInt(params.productId)
  
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null)
  const [deletingVariant, setDeletingVariant] = useState<ProductVariant | null>(null)

  const { data: product, isLoading: productLoading } = useProduct(productId)
  const { data: variants, isLoading: variantsLoading, refetch } = useProductVariants(productId)
  const createVariantMutation = useCreateVariant()
  const deleteVariantMutation = useDeleteVariant()

  const handleDeleteVariant = async () => {
    if (!deletingVariant) return
    
    try {
      await deleteVariantMutation.mutateAsync(deletingVariant.id)
      setDeletingVariant(null)
      refetch()
    } catch (error) {
      console.error('Failed to delete variant:', error)
    }
  }

  const columns = [
    {
      key: 'sku',
      title: 'SKU',
      render: (variant: ProductVariant) => (
        <div className={styles.skuCell}>
          <span className={styles.sku}>{variant.sku}</span>
        </div>
      ),
    },
    {
      key: 'attributes',
      title: 'Attributes',
      render: (variant: ProductVariant) => (
        <div className={styles.attributesCell}>
          {variant.attribute_values?.map((attr, index) => (
            <Badge key={index} variant="secondary" size="sm">
              {attr.attribute.name}: {attr.value}
            </Badge>
          )) || <span className={styles.noAttributes}>No attributes</span>}
        </div>
      ),
    },
    {
      key: 'price',
      title: 'Price',
      render: (variant: ProductVariant) => (
        <div className={styles.priceCell}>
          <span className={styles.price}>
            {formatCurrency(variant.price)}
          </span>
          {variant.compare_at_price && variant.compare_at_price > variant.price && (
            <span className={styles.comparePrice}>
              {formatCurrency(variant.compare_at_price)}
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'inventory',
      title: 'Inventory',
      render: (variant: ProductVariant) => (
        <div className={styles.inventoryCell}>
          <span className={styles.quantity}>{variant.quantity_available}</span>
          <StockStatusBadge quantity={variant.quantity_available} />
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      render: (variant: ProductVariant) => (
        <Badge variant={variant.is_active ? 'success' : 'error'}>
          {variant.is_active ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (variant: ProductVariant) => (
        <div className={styles.actions}>
          <PermissionGuard permission="staff.change_productvariant">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingVariant(variant)}
              title="Edit variant"
            >
              <FiEdit />
            </Button>
          </PermissionGuard>
          
          <PermissionGuard permission="staff.delete_productvariant">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDeletingVariant(variant)}
              title="Delete variant"
            >
              <FiTrash2 />
            </Button>
          </PermissionGuard>
        </div>
      ),
    },
  ]

  if (productLoading) {
    return <PageLoading message="Loading product..." />
  }

  if (!product) {
    return (
      <div className={styles.error}>
        <Card padding="lg">
          <h2>Product Not Found</h2>
          <p>The requested product could not be found.</p>
          <Button as={Link} to="/products" variant="primary">
            Back to Products
          </Button>
        </Card>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button
            as={Link}
            to="/products"
            variant="ghost"
            className={styles.backButton}
          >
            <FiArrowLeft />
            Back to Products
          </Button>
          
          <div className={styles.titleSection}>
            <h1 className={styles.title}>Product Variants</h1>
            <p className={styles.subtitle}>
              Manage variants for "{product.name}"
            </p>
          </div>
        </div>
        
        <div className={styles.headerActions}>
          <PermissionGuard permission="staff.add_productvariant">
            <Button
              variant="primary"
              onClick={() => setIsCreateModalOpen(true)}
              className={styles.createButton}
            >
              <FiPlus />
              Add Variant
            </Button>
          </PermissionGuard>
        </div>
      </div>

      <div className={styles.productInfo}>
        <Card className={styles.productCard}>
          <div className={styles.productHeader}>
            <div className={styles.productIcon}>
              <FiPackage />
            </div>
            <div className={styles.productDetails}>
              <h3>{product.name}</h3>
              <p>SKU: {product.slug}</p>
              <p>Category: {product.category.name}</p>
              <p>Brand: {product.brand.name}</p>
            </div>
            <div className={styles.productStats}>
              <div className={styles.stat}>
                <span className={styles.statValue}>{variants?.length || 0}</span>
                <span className={styles.statLabel}>Variants</span>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <Card className={styles.tableCard}>
        <Card.Header>
          <h2>Variants</h2>
          <p>Manage product variants, pricing, and inventory</p>
        </Card.Header>
        
        <Card.Body className={styles.tableBody}>
          <DataTable
            data={variants || []}
            columns={columns}
            loading={variantsLoading}
            emptyMessage="No variants found. Create your first variant to get started."
          />
        </Card.Body>
      </Card>

      {/* Create Variant Modal */}
      <VariantFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        productId={productId}
        mode="create"
        onSuccess={() => {
          setIsCreateModalOpen(false)
          refetch()
        }}
      />

      {/* Edit Variant Modal */}
      {editingVariant && (
        <VariantFormModal
          isOpen={true}
          onClose={() => setEditingVariant(null)}
          productId={productId}
          variant={editingVariant}
          mode="edit"
          onSuccess={() => {
            setEditingVariant(null)
            refetch()
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingVariant}
        onClose={() => setDeletingVariant(null)}
        title="Delete Variant"
        size="sm"
      >
        <div className={styles.deleteModal}>
          <p>
            Are you sure you want to delete the variant "{deletingVariant?.sku}"? 
            This action cannot be undone.
          </p>
          
          <div className={styles.deleteActions}>
            <Button
              variant="outline"
              onClick={() => setDeletingVariant(null)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleDeleteVariant}
              disabled={deleteVariantMutation.isPending}
            >
              {deleteVariantMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}
