// New product route
// Protected route for creating new products

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../components/auth/AuthGuard'
import { ProductFormPage } from '../../pages/products/ProductFormPage'

export const Route = createFileRoute('/products/new')({
  component: NewProductRoute,
})

function NewProductRoute() {
  return (
    <AuthGuard permission="staff.add_productproxy">
      <ProductFormPage mode="create" />
    </AuthGuard>
  )
}
