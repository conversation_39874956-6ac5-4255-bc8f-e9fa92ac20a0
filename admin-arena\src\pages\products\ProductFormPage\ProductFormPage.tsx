// Product form page for creating and editing products
// Comprehensive form with validation and image upload

import React, { useEffect } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiSave, FiArrowLeft } from 'react-icons/fi'
import {
  useProduct,
  useCreateProduct,
  useUpdateProduct,
  useProductCategories,
  useProductBrands,
  useProductTypes
} from '../../../hooks/use-products'
import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { PageLoading, ButtonLoading } from '../../../components/ui/LoadingSpinner'
import styles from './ProductFormPage.module.scss'

const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  slug: z.string().min(1, 'Product slug is required'),
  description: z.string().min(1, 'Product description is required'),
  category: z.number().min(1, 'Category is required'),
  brand: z.number().min(1, 'Brand is required'),
  product_type: z.number().min(1, 'Product type is required'),
  is_active: z.boolean(),
})

type ProductFormData = z.infer<typeof productSchema>

interface ProductFormPageProps {
  mode: 'create' | 'edit'
  productId?: number
}

export const ProductFormPage: React.FC<ProductFormPageProps> = ({ mode, productId }) => {
  const navigate = useNavigate()

  const { data: product, isLoading: productLoading } = useProduct(productId!)
  const { data: categories } = useProductCategories()
  const { data: brands } = useProductBrands()
  const { data: productTypes } = useProductTypes()

  const createProductMutation = useCreateProduct()
  const updateProductMutation = useUpdateProduct(productId!)

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      slug: '',
      description: '',
      category: 0,
      brand: 0,
      product_type: 0,
      is_active: true,
    },
  })

  // Update form when product data loads
  useEffect(() => {
    if (mode === 'edit' && product) {
      form.reset({
        name: product.title || '',
        slug: product.slug || '',
        description: product.description || '',
        category: product.category || 0,
        brand: product.brand || 0,
        product_type: product.product_type || 0,
        is_active: product.is_active,
      })
    }
  }, [product, mode, form])

  // Auto-generate slug from name
  const handleNameChange = (name: string) => {
    if (mode === 'create') {
      const slug = name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
      form.setValue('slug', slug)
    }
  }

  const handleSubmit = async (data: ProductFormData) => {
    try {
      if (mode === 'create') {
        await createProductMutation.mutateAsync(data)
        navigate({ to: '/products' })
      } else {
        await updateProductMutation.mutateAsync(data)
        navigate({ to: '/products' })
      }
    } catch (error) {
      console.error('Form submission failed:', error)
    }
  }

  const isLoading = productLoading || createProductMutation.isPending || updateProductMutation.isPending

  if (mode === 'edit' && productLoading) {
    return <PageLoading message="Loading product..." />
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button
            variant="ghost"
            onClick={() => navigate({ to: '/products' })}
            className={styles.backButton}
          >
            <FiArrowLeft />
            Back to Products
          </Button>

          <div className={styles.titleSection}>
            <h1 className={styles.title}>
              {mode === 'create' ? 'Create Product' : 'Edit Product'}
            </h1>
            <p className={styles.subtitle}>
              {mode === 'create'
                ? 'Add a new product to your catalog'
                : 'Update product information and settings'
              }
            </p>
          </div>
        </div>

        <div className={styles.headerActions}>
          <Button
            variant="outline"
            onClick={() => navigate({ to: '/products' })}
          >
            Cancel
          </Button>

          <Button
            type="submit"
            variant="primary"
            form="product-form"
            disabled={isLoading}
          >
            <ButtonLoading
              isLoading={isLoading}
              loadingText={mode === 'create' ? 'Creating...' : 'Saving...'}
            >
              <FiSave />
              {mode === 'create' ? 'Create Product' : 'Save Changes'}
            </ButtonLoading>
          </Button>
        </div>
      </div>

      <form
        id="product-form"
        onSubmit={form.handleSubmit(handleSubmit)}
        className={styles.form}
      >
        <div className={styles.formGrid}>
          <Card className={styles.mainCard}>
            <CardHeader>
              <h2>Basic Information</h2>
            </CardHeader>

            <CardBody>
              <div className={styles.formSection}>
                <div className={styles.formGroup}>
                  <label htmlFor="name">Product Name *</label>
                  <Input
                    id="name"
                    {...form.register('name')}
                    onChange={(e) => {
                      form.setValue('name', e.target.value)
                      handleNameChange(e.target.value)
                    }}
                    error={form.formState.errors.name?.message}
                    placeholder="Enter product name"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="slug">Product Slug *</label>
                  <Input
                    id="slug"
                    {...form.register('slug')}
                    error={form.formState.errors.slug?.message}
                    placeholder="product-slug"
                    helperText="URL-friendly version of the product name"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="description">Description *</label>
                  <textarea
                    id="description"
                    {...form.register('description')}
                    className={styles.textarea}
                    rows={4}
                    placeholder="Enter product description"
                  />
                  {form.formState.errors.description && (
                    <span className={styles.error}>
                      {form.formState.errors.description.message}
                    </span>
                  )}
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className={styles.sideCard}>
            <CardHeader>
              <h2>Product Settings</h2>
            </CardHeader>

            <CardBody>
              <div className={styles.formSection}>
                <div className={styles.formGroup}>
                  <label htmlFor="category">Category *</label>
                  <Controller
                    name="category"
                    control={form.control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className={styles.select}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      >
                        <option value="">Select category</option>
                        {categories?.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.title}
                          </option>
                        ))}
                      </select>
                    )}
                  />
                  {form.formState.errors.category && (
                    <span className={styles.error}>
                      {form.formState.errors.category.message}
                    </span>
                  )}
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="brand">Brand *</label>
                  <Controller
                    name="brand"
                    control={form.control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className={styles.select}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      >
                        <option value="">Select brand</option>
                        {brands?.map((brand) => (
                          <option key={brand.id} value={brand.id}>
                            {brand.title}
                          </option>
                        ))}
                      </select>
                    )}
                  />
                  {form.formState.errors.brand && (
                    <span className={styles.error}>
                      {form.formState.errors.brand.message}
                    </span>
                  )}
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="product_type">Product Type *</label>
                  <Controller
                    name="product_type"
                    control={form.control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className={styles.select}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      >
                        <option value="">Select product type</option>
                        {productTypes?.map((type) => (
                          <option key={type.id} value={type.id}>
                            {type.title}
                          </option>
                        ))}
                      </select>
                    )}
                  />
                  {form.formState.errors.product_type && (
                    <span className={styles.error}>
                      {form.formState.errors.product_type.message}
                    </span>
                  )}
                </div>

                <div className={styles.formGroup}>
                  <label className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      {...form.register('is_active')}
                    />
                    <span>Active Product</span>
                  </label>
                  <p className={styles.help}>
                    Inactive products won't be visible to customers
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </form>
    </div>
  )
}
