// Edit product route
// Protected route for editing existing products

import { createFileRoute } from '@tanstack/react-router'
import { AuthGuard } from '../../../components/auth/AuthGuard'
import { ProductFormPage } from '../../../pages/products/ProductFormPage'

export const Route = createFileRoute('/products/$productId/edit')({
  component: EditProductRoute,
})

function EditProductRoute() {
  return (
    <AuthGuard permission="staff.change_productproxy">
      <ProductFormPage mode="edit" />
    </AuthGuard>
  )
}
